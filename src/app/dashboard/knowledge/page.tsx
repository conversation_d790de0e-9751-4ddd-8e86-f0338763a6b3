'use client'

import { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useDashboardData, updateFaqCountInCache } from '@/hooks/useOptimizedData'
import { v4 as uuidv4 } from 'uuid'
import { FaBrain, FaHeadphones, FaPlay, FaEdit, FaTrash, FaImage } from 'react-icons/fa'; // Updated icons
import { nanoid } from 'nanoid';
import { sendFaqWebhook } from '@/app/api/webhooks/faq';
import { useLanguage } from '@/context/LanguageContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization';

// Memoized dummy data for knowledge base questions
const useDummyQuestions = () => {
  return useMemo(() =>
    Array.from({ length: 90 }, (_, i) => ({
      id: i + 1,
      question: `How does your product ${i % 3 === 0 ? 'handle customer data security' : i % 3 === 1 ? 'compare to competitors' : 'benefit my business'}? ${i % 5 === 0 ? 'I need detailed information about the features, benefits, and limitations.' : ''}`,
      answer: `Our product ${i % 3 === 0 ? 'uses end-to-end encryption to secure all customer data and complies with GDPR and other privacy regulations' : i % 3 === 1 ? 'offers 24/7 support, better pricing, and more features than competitors in the same category' : 'increases efficiency by 40% and reduces operational costs significantly'}. ${i % 4 === 0 ? 'We also provide comprehensive documentation and training materials to help you get the most out of our services.' : ''}`,
      dateAdded: new Date(2025, 3, Math.floor(Math.random() * 30) + 1).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }),
      isActive: Math.random() > 0.2
    })),
    [] // Empty dependency array ensures data is only created once
  );
}

// Define the type for uploaded PDFs including the File object
// type UploadedPdf = {
//   id: number;
//   name: string;
//   size: string;
//   date: string;
//   file: File; // Add the File object
// };

// Define the type for photo info in QA items
interface PhotoInfo {
  id: number;
  photo_id: string;
  photo_url: string | null; // Single thumbnail for display
  full_photo_urls: string[] | null; // Complete array of photos
}

// Define the type for editing item
interface EditItem {
  id: number;
  field: 'question' | 'answer';
  value: string;
}

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  return (
    <div
      className={`${className} bg-zinc-700 rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 bg-zinc-600 transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-white/5 text-zinc-400 text-xs">
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

export default function KnowledgePage() {
  const supabase = createClientComponentClient()
  const { t } = useLanguage()

  // Use dashboard cache for knowledge stats - single source of truth
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  const [isUploading, setIsUploading] = useState(false)
  const [question, setQuestion] = useState<string>('')
  const [answer, setAnswer] = useState<string>('')
  const [recentQA, setRecentQA] = useState<Array<{
    id: number,
    question: string,
    answer: string,
    audioUrl?: string,
    audioDuration?: number,
    photoInfo?: PhotoInfo
  }>>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingItem, setEditingItem] = useState<EditItem | null>(null)
  const [hasFocusedInput, setHasFocusedInput] = useState<boolean>(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)
  const [showAudioDeleteConfirmation, setShowAudioDeleteConfirmation] = useState(false)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)

  // Get knowledge stats from dashboard cache instead of separate state
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const photoUsagePercentage = knowledgeStats?.photoUsagePercentage || 0
  const isLoadingCount = isDashboardLoading

  // Add new state for photo search
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [photoSearchResults, setPhotoSearchResults] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])
  const [selectedPhoto, setSelectedPhoto] = useState<{
    id: number,
    photo_id: string,
    photo_url: string | null,
    full_photo_urls: string[] | null
  } | null>(null)
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)
  const [showPhotoResults, setShowPhotoResults] = useState(false)
  const searchResultsRef = useRef<HTMLDivElement>(null)

  const modalRef = useRef<HTMLDivElement>(null)
  const faqStatusOverlayRef = useRef<HTMLDivElement>(null); // Keep this one
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const questionInputRef = useRef<HTMLInputElement>(null)
  const answerInputRef = useRef<HTMLInputElement>(null)

  // Viewing popup state
  const [viewingItem, setViewingItem] = useState<{field: 'question' | 'answer', value: string} | null>(null)
  const viewModalRef = useRef<HTMLDivElement>(null)

  // Image gallery state
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Audio recording state
  const [isRecording, setIsRecording] = useState(false)
  const [recordingFor, setRecordingFor] = useState<'business' | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [audioRecorder, setAudioRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  const [businessAudioUrl, setBusinessAudioUrl] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState<'business' | null>(null)
  const [playbackTime, setPlaybackTime] = useState(0)
  const [audioDuration, setAudioDuration] = useState(0)
  const [playbackProgress, setPlaybackProgress] = useState(0)
  const [isSaving, setIsSaving] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const playbackTimerRef = useRef<number | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)



  // Remove PDF upload state
  // const [uploadedPdfs, setUploadedPdfs] = useState<Array<{id: number, name: string, size: string, date: string, file: File}>>([]);
  // const fileInputRef = useRef<HTMLInputElement>(null)
  // const [dragActive, setDragActive] = useState(false)

  // Add a new state for recording errors
  const [recordingError, setRecordingError] = useState<string | null>(null)

  // Add a new state for showing audio error popup
  const [showAudioErrorPopup, setShowAudioErrorPopup] = useState(false)

  // Add a new state for tracking which QA item is playing
  const [playingQAId, setPlayingQAId] = useState<number | null>(null)

  // Add these new state variables in the component
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [audioInitialized, setAudioInitialized] = useState(false);

  // Remove state for document saving
  // const [isDocUpdating, setIsDocUpdating] = useState(false)
  // const [showDocConfirmation, setShowDocConfirmation] = useState(false)
  // const [docUpdateStatus, setDocUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  // const [docUpdateMessage, setDocUpdateMessage] = useState('')
  // const [docUpdateProgress, setDocUpdateProgress] = useState(0)

  // Move stopPlayback definition higher up
  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0; // Reset playback to start
    }
    if (playbackTimerRef.current) {
      cancelAnimationFrame(playbackTimerRef.current);
      playbackTimerRef.current = null;
    }
    setIsPlaying(null); // Stop for specific type
    setPlayingQAId(null); // Stop for recent QA
    setPlaybackTime(0);
    setPlaybackProgress(0);
  };

  // Removed fetchFaqCount - now using dashboard cache

  // Removed fetchSubscriptionLimits - now using dashboard cache



  // Removed fetchPhotoCount - now using dashboard cache

  // Remove function to fetch document count
  /*
  const fetchDocCount = async () => {
    setIsLoadingDocCount(true)
    try {
      const clientId = getClientInfo()?.client_id;

      if (!clientId) {
        console.error('Client ID not found while fetching document count');
        setTotalDocsCount(0);
        return;
      }

      const { count, error } = await supabase
        .from('docs')
        .select('*', { count: 'exact', head: true })
        .eq('client_id', clientId);

      if (error) {
        console.error('Error fetching document count:', error);
        return;
      }

      setTotalDocsCount(count || 0);
    } catch (error) {
      console.error('Error in fetchDocCount:', error);
    } finally {
      setIsLoadingDocCount(false);
    }
  }
  */


  // Removed useEffect for manual fetching - now using dashboard cache





  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Batch state updates
      if (editingItem.id === -1) {
        setQuestion(editingItem.value);
      } else if (editingItem.id === -2) {
        setAnswer(editingItem.value);
      } else {
        // Use functional update to avoid dependency on recentQA
        setRecentQA(prev =>
          prev.map(qa =>
            qa.id === editingItem.id
              ? { ...qa, [editingItem.field]: editingItem.value }
              : qa
          )
        );
      }

      // Close modal after state updates
      setEditingItem(null);
      setHasFocusedInput(false);
    });
  }, [editingItem]);







  // Add effect to focus textarea and set cursor at the end when editing
  useEffect(() => {
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // On desktop: auto-focus and set cursor at the end
        textareaRef.current.focus()
        const length = textareaRef.current.value.length
        textareaRef.current.setSelectionRange(length, length)
        setHasFocusedInput(true)
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);

  // Clean up audio recording on unmount
  useEffect(() => {
    return () => {
      // Stop recording if it's in progress
      if (isRecording && audioRecorder) {
        audioRecorder.stop();
      }

      // Stop the timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Stop all audio tracks
      if (audioStream) {
        audioStream.getAudioTracks().forEach(track => track.stop());
      }
    };
  }, [isRecording, audioRecorder, audioStream]);

  // Function to focus input and set cursor at the end
  const focusInputAtEnd = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.focus()
      const length = ref.current.value.length
      ref.current.setSelectionRange(length, length)
    }
  }

  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  // handleSaveEdit is now defined above with useCallback

  const handleViewItem = (field: 'question' | 'answer', value: string) => {
    setViewingItem({ field, value })
  }

  const knowledgeItems = [
    {
      id: 1,
      title: 'Company FAQ',
      itemCount: 24,
      dateAdded: 'Apr 12, 2025',
      isActive: true
    },
    {
      id: 2,
      title: 'Product Information',
      itemCount: 36,
      dateAdded: 'Apr 10, 2025',
      isActive: true
    },
    {
      id: 3,
      title: 'Return Policy',
      itemCount: 8,
      dateAdded: 'Apr 5, 2025',
      isActive: false
    }
  ]

  const handleAddQA = () => {
    if (question.trim() && (answer.trim() || businessAudioUrl)) {
      setRecentQA(prev => [...prev, {
        id: Date.now(),
        question: question.trim(),
        answer: businessAudioUrl ? `[AUDIO:${audioDuration}s]` : answer.trim(),
        audioUrl: businessAudioUrl || undefined,
        audioDuration: businessAudioUrl ? audioDuration : undefined,
        // Include photo info if a photo is selected
        photoInfo: selectedPhoto ? {
          id: selectedPhoto.id,
          photo_id: selectedPhoto.photo_id,
          photo_url: selectedPhoto.photo_url,
          full_photo_urls: selectedPhoto.full_photo_urls
        } : undefined
      }]);

      // Reset fields after adding
      setQuestion('');
      setAnswer('');
      if (businessAudioUrl) {
        // Don't revoke URL yet since we're storing it in recentQA
        setBusinessAudioUrl(null);
      }
      // Reset the selected photo as well
      setSelectedPhoto(null);
      setRecordingError(null);
    }
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (recentQA.length === 0) {
      setUpdateMessage(t('no_questions_update'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    setShowConfirmation(true)
  }

  // Helper function (ensure this exists within the KnowledgePage component or is imported)
  const blobToBase64 = (blob: Blob): Promise<string> => {
      return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onerror = reject;
          reader.onload = () => {
              const base64String = reader.result as string;
              resolve(base64String.split(',')[1]); // Remove the data URL prefix
          };
          reader.readAsDataURL(blob);
      });
  }

  // Replace the entire existing saveQAToSupabase function with this implementation
  const saveQAToSupabase = async () => {
    setShowConfirmation(false);
    setIsUpdating(true);
    setUpdateStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(t('processing_additions'));

    const totalItems = recentQA.length;
    if (totalItems === 0) {
      setUpdateMessage(t('no_questions_update'));
      setUpdateStatus('error');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      setIsUpdating(false);
      return;
    }

    let itemsProcessed = 0;
    const successfullyProcessedIds = new Set<number>();

    try {
      // Get User ID and Client ID once
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.user?.id) {
        throw new Error(sessionError?.message || 'Could not verify user session. Please try again.');
      }
      const userId = session.user.id;

      const clientId = clientInfo?.client_id;

      if (!clientId) {
        throw new Error('Client identifier not found. Please ensure you are properly logged in.');
      }

      // IMPORTANT: Verify that the client_id belongs to the current authenticated user
      // This is required for the RLS policy to allow the operation
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('client_id')
        .eq('client_id', clientId)
        .eq('auth_id', userId)
        .single();

      if (clientError || !clientData) {
        console.error('Client verification error:', clientError);
        throw new Error('You do not have permission to update FAQs for this client. Please contact support.');
      }

      const bucketName = 'audios'; // Define the target bucket

      // --- Process each QA item individually ---
      for (const qa of recentQA) {
        let targetFilePath: string | null = null;
        let publicAudioUrl: string | null = null;
        let audioDurationValue: number | null = qa.audioDuration || null;

        // --- Direct upload to Supabase (if applicable) ---
        if (qa.audioUrl && qa.audioUrl.startsWith('blob:')) {
          setUpdateMessage(`Uploading audio for "${qa.question.substring(0, 20)}..."`);
          try {
            // 1. Fetch Blob
            const response = await fetch(qa.audioUrl);
            const audioBlob = await response.blob();

            if (audioBlob.size === 0) {
              itemsProcessed++;
              setUpdateProgress(Math.round((itemsProcessed / totalItems) * 100));
              continue; // Skip to next QA item
            }

            // 2. Determine File Extension & Mime Type
            const mimeType = audioBlob.type || 'application/octet-stream';
            let fileExtension = 'bin';
            const mimeParts = mimeType.split('/');
            if (mimeParts.length > 1) {
              const subType = mimeParts[1].split(';')[0]; // Handle potential codecs string
              if (subType === 'mp4') fileExtension = 'mp4';
              else if (subType === 'webm') fileExtension = 'webm';
              else if (subType === 'opus') fileExtension = 'opus';
              else if (subType === 'ogg') fileExtension = 'oga'; // Changed to oga for consistency
              else fileExtension = subType; // Use subtype if known
            }

            // 3. Generate Target File Path
            const uniqueFileName = `${uuidv4()}.${fileExtension}`;
            targetFilePath = `${userId}/${uniqueFileName}`; // Store for later use

            // 4. DIRECT UPLOAD to Supabase Storage
            const { data: uploadData, error: uploadError } = await supabase
              .storage
              .from(bucketName)
              .upload(targetFilePath, audioBlob, {
                cacheControl: '3600',
                contentType: mimeType,
                upsert: false
              });

            if (uploadError) {
              console.error("Upload error:", uploadError);
              throw new Error(`Failed to upload audio: ${uploadError.message}`);
            }

            // 5. Get Public URL
            const { data: urlData } = supabase
              .storage
              .from(bucketName)
              .getPublicUrl(targetFilePath);

            if (!urlData?.publicUrl) {
              throw new Error(`Could not get public URL for ${targetFilePath}`);
            }

            publicAudioUrl = urlData.publicUrl;

          } catch (uploadError) {
            console.error(`Error uploading audio for QA ID ${qa.id}:`, uploadError);
            throw uploadError;
          }
        } else {
          setUpdateMessage(`Saving text answer for "${qa.question.substring(0, 20)}..."`);
          // This is a text-only item, targetFilePath remains null
        }

        // --- Stage 2: Insert FAQ Data into Database ---
        // Get sector from client info (from dashboard cache)
        const sector = clientInfo?.sector;
        const lang = clientInfo?.lang;

        const faq_id = nanoid(9); // Generate unique ID for the FAQ

        // Prepare the FAQ object - always use question_p and answer_p fields
        const faqToInsert = {
          faq_id: faq_id,
          created_at: new Date(),
          client_id: clientId,
          question_p: qa.question,
          answer_p: publicAudioUrl ? "" : qa.answer,
          audio_url: publicAudioUrl,
          audio_file_path: targetFilePath,
          audio_duration: publicAudioUrl ? Math.round(audioDurationValue ?? 0) : null,
          photo_url: qa.photoInfo?.full_photo_urls || (qa.photoInfo?.photo_url ? [qa.photoInfo.photo_url] : null),
          photo_id: qa.photoInfo?.photo_id || null
        };

        const { error: insertError } = await supabase
          .from('faqs')
          .insert(faqToInsert);

        if (insertError) {
          console.error('Error inserting FAQ:', insertError);
          const firstQuestion = qa.question.substring(0, 20) || 'N/A';
          if (insertError.message.includes('violates not-null constraint') && insertError.message.includes('"answer_p"')) {
            throw new Error(`Database requires an answer text. Cannot save audio-only entry for "${firstQuestion}...".`);
          }
          throw new Error(`Failed to save question "${firstQuestion}...". ${insertError.message}`);
        } else {
          // After successful insert, trigger the webhook (fire and forget)
          sendFaqWebhook({
            faq_id: faq_id,
            client_id: clientId,
            question: qa.question,
            answer: qa.answer,
            sector: sector,
            lang: lang || 'en', // Send language so webhook knows which fields were used
            audioUrl: publicAudioUrl // Send the actual audio URL instead of a boolean flag
          });
        }

        // Mark as processed and update progress
        successfullyProcessedIds.add(qa.id);
        itemsProcessed++;
        setUpdateProgress(Math.round((itemsProcessed / totalItems) * 100));

        // Add a small delay before processing the next item
        if (itemsProcessed < totalItems) {
          await new Promise(resolve => setTimeout(resolve, 300)); // 300ms delay
        }

      }

      // --- Stage 3: Finalize ---
      setUpdateMessage(t('finalizing'));

      // Update FAQ count in dashboard cache immediately
      const newFaqCount = totalFaqs + itemsProcessed;
      updateFaqCountInCache(newFaqCount);

      // Clean up blob URLs for successfully processed items
      recentQA.forEach(qa => {
        if (successfullyProcessedIds.has(qa.id) && qa.audioUrl && qa.audioUrl.startsWith('blob:')) {
          URL.revokeObjectURL(qa.audioUrl);
        }
      });

      setUpdateStatus('success');
      setUpdateMessage(`${t('update_success').replace('{count}', itemsProcessed.toString()).replace('{plural}', itemsProcessed > 1 ? 's' : '')}`);
      // Remove successfully processed items from the list
      setRecentQA(prev => prev.filter(qa => !successfullyProcessedIds.has(qa.id)));

      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 1500);

    } catch (error: any) {
      console.error('Error updating knowledge base:', error);
      setUpdateStatus('error');
      // Keep unprocessed items in the list on error
      setRecentQA(prev => prev.filter(qa => !successfullyProcessedIds.has(qa.id)));
      setUpdateMessage(error.message || 'Failed to update knowledge base. Please check logs and try again.');
      // Keep modal open longer on error
      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 5000);
    }
  }

  const handleDelete = (id: number) => {
    // Show confirmation modal instead of deleting immediately
    setItemToDelete(id);
    setShowDeleteConfirmation(true);
  }

  // Function to handle actual deletion after confirmation
  const confirmDelete = () => {
    if (itemToDelete === null) return;

    const qaToDelete = recentQA.find(qa => qa.id === itemToDelete);

    // Clean up audio URL if it exists
    if (qaToDelete?.audioUrl) {
      URL.revokeObjectURL(qaToDelete.audioUrl);
    }

    // Remove item after confirmation
    setRecentQA(prev => prev.filter(qa => qa.id !== itemToDelete));

    // Close the confirmation modal
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  }

  // Function to handle audio deletion after confirmation
  const confirmAudioDelete = () => {
    if (businessAudioUrl) {
      URL.revokeObjectURL(businessAudioUrl);
      setBusinessAudioUrl(null);
      setRecordingError(null); // Clear error when deleting
      setAnswer(''); // Reset the answer field
    }

    // Close the confirmation modal
    setShowAudioDeleteConfirmation(false);
  }

  // Function to handle closing the audio error popup
  const handleCloseAudioErrorPopup = () => {
    // Clear the audio recording
    if (businessAudioUrl) {
      URL.revokeObjectURL(businessAudioUrl);
      setBusinessAudioUrl(null);
    }

    // Clear the error and reset the answer field
    setRecordingError(null);
    setAnswer('');

    // Close the popup
    setShowAudioErrorPopup(false);
  }



  // Remove PDF upload handlers
  /*
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(Array.from(e.target.files))
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = () => {
    setDragActive(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(Array.from(e.dataTransfer.files))
    }
  }

  // Update processFiles function to store the File object
  const processFiles = (files: File[]) => {
    const pdfFiles = files.filter(file => file.type === 'application/pdf');
    if (pdfFiles.length === 0) {
      alert('Only PDF files are allowed');
      return;
    }

    // Store file metadata along with the File object
    const newPdfs = pdfFiles.map(file => ({
      id: Date.now() + Math.random(),
      name: file.name,
      size: formatFileSize(file.size),
      date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }),
      file: file // Store the actual File object
    }));

    setUploadedPdfs(prev => [...newPdfs, ...prev]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B'
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB'
    else return (bytes / 1048576).toFixed(1) + ' MB'
  }

  const handleDeletePdf = (id: number) => {
    setUploadedPdfs(prev => prev.filter(pdf => pdf.id !== id))
  }

  const handleBrowseClick = () => {
    fileInputRef.current?.click()
  }
  */

  // Audio recording functions
  const startRecording = async (type: 'business') => {
    try {
      // Clean up previous recording if it exists
      if (businessAudioUrl) {
        URL.revokeObjectURL(businessAudioUrl);
        setBusinessAudioUrl(null);
      }

      // Reset answer and stop any existing recording
      setAnswer('');
      if (isRecording) {
        stopRecording();
      }

      setRecordingTime(0);

      // Enhanced audio constraints for better quality
      const audioConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          sampleSize: 16,
          channelCount: 1  // Mono for speech clarity
        }
      };

      // Get audio stream with enhanced constraints
      const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
      setAudioStream(stream);
      setRecordingFor(type);

      // Prefer high-quality codec options
      const supportedTypes = [
        'audio/mp4;codecs=mp4a.40.2', // AAC encoding
        'audio/mp4',
        'audio/webm;codecs=opus', // Best quality for voice
        'audio/webm',
      ];

      let selectedMimeType = '';
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          selectedMimeType = type;
          break;
        }
      }

      if (!selectedMimeType) {
        console.error("[startRecording] No supported mimeType found for MediaRecorder!");
        alert("Your browser doesn't support audio recording in a compatible format.");
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
        return;
      }

      const chunks: Blob[] = [];
      setAudioChunks([]);

      // Set higher bitrate for better quality
      const recorderOptions = {
        mimeType: selectedMimeType,
        audioBitsPerSecond: 128000  // 128kbps for good voice quality
      };

      const recorder = new MediaRecorder(stream, recorderOptions);
      setAudioRecorder(recorder);

      recorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data);
        }
      };

      recorder.onstop = () => {
        // Use the same selectedMimeType when creating the Blob
        const audioBlob = new Blob(chunks, { type: selectedMimeType });
        const audioUrl = URL.createObjectURL(audioBlob);

        setBusinessAudioUrl(audioUrl);

        // Stop all audio tracks
        if (audioStream) {
          audioStream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        }
      };

      // Set recording state
      setIsRecording(true);

      // Stop any existing timer first
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Start recorder before timer to ensure everything is ready
      recorder.start();

      // IMPORTANT: We need a small delay to ensure React state updates have propagated
      // This is crucial for the timer to work correctly
      setTimeout(() => {
        // Start a new timer
        timerRef.current = setInterval(() => {
          setRecordingTime(prev => {
            return prev + 1;
          });
        }, 1000);
      }, 100);

    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Unable to access microphone. Please check your browser permissions.');
    }
  };

  const stopRecording = () => {
    // Ensure the recorder exists and is active
    if (audioRecorder && audioRecorder.state !== 'inactive') {

      try {
        // Save the current recording time as duration
        setAudioDuration(recordingTime);

        // Stop the timer first
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // Stop the recorder - this will trigger the onstop handler we defined in startRecording
        audioRecorder.stop();

        // Make sure we set the recording state to false
        setIsRecording(false);
        setRecordingFor(null);

        // Show saving indicator AFTER setting isRecording to false
        setIsSaving(true);
        setTimeout(() => {
          setIsSaving(false);

          // Check if recording is too long after delay
          if (recordingTime > 60) {
            setRecordingError("Over 60s");
            setShowAudioErrorPopup(true); // Show the error popup instead of just setting the error message
          } else {
            setRecordingError(null); // Clear error when deleting
            setAnswer(''); // Reset the answer field
          }
        }, 1500);
      } catch (error) {
        console.error('Error stopping recording:', error);
      }
    } else {
      // Clean up anyway
      setIsRecording(false);
      setRecordingFor(null);

      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // Add a function to initialize audio on first user interaction
  const initializeAudio = () => {
    // Check if already initialized or if running on server
    if (audioInitialized || typeof window === 'undefined') return;

    try {
      // Create AudioContext after user interaction
      const context = new (window.AudioContext || (window as any).webkitAudioContext)();
      setAudioContext(context);
      setAudioInitialized(true);

      // Optional: Resume context if it's suspended (iOS sometimes starts it suspended)
      if (context.state === 'suspended') {
        context.resume().then(() => {
          console.log('AudioContext resumed successfully.');
        }).catch(e => console.error('Error resuming AudioContext:', e));
      }
    } catch (e) {
      console.error('Error initializing AudioContext:', e);
      setRecordingError('Failed to initialize audio. Please ensure your browser supports Web Audio API and permissions are granted.');
    }
  };

  // Effect to initialize audio on first user interaction (if not already initialized)
  useEffect(() => {
    const initAudioOnInteraction = () => {
      if (!audioInitialized) {
        initializeAudio();
        // Remove the listener after the first interaction
        window.removeEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
        window.removeEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
      }
    };

    // Add listeners for the first user interaction
    // Using { once: true } to ensure it only runs once
    if (typeof window !== 'undefined' && !audioInitialized) {
       window.addEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
       window.addEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
    }

    // Cleanup listeners on component unmount
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('touchstart', initAudioOnInteraction);
        window.removeEventListener('mousedown', initAudioOnInteraction);
      }
    };
  }, [audioInitialized, initializeAudio]); // Add initializeAudio dependency

  // Add document visibility change listener
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPlayback();
        // Optionally stop recording if needed when tab becomes inactive
        // if (isRecording) {
        //   stopRecording();
        // }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [stopPlayback]); // Added stopPlayback dependency

  // Update the playRecording function
  const playRecording = (type: 'business') => {
    // Initialize audio on first interaction or if context is suspended
    initializeAudio();
    if (audioContext?.state === 'suspended') {
      audioContext.resume().catch(e => console.error('[playRecording] Error resuming suspended AudioContext:', e));
    }

    const audioUrl = businessAudioUrl;
    if (!audioUrl) {
      return;
    }

    // Check if already playing - just stop if that's the case
    if (isPlaying === type) {
      stopPlayback();
      return;
    }

    // Stop any existing playback
    stopPlayback(); // Use the centralized stop function

    const audio = new Audio();
    audioRef.current = audio; // Store ref immediately

    // Set state before playing
    setIsPlaying(type);
    setPlaybackTime(audioDuration);
    setPlaybackProgress(0);

    // Setup event listeners
    audio.addEventListener('timeupdate', () => {
      if (!audioRef.current) return; // Check ref
      const currentTime = audioRef.current.currentTime;
      setPlaybackTime(Math.ceil(audioDuration - currentTime));
      setPlaybackProgress(Math.min(100, (currentTime / audioDuration) * 100));
    });

    audio.addEventListener('ended', () => {
      stopPlayback(); // Use centralized stop function on end
    });

    audio.addEventListener('error', (e) => {
      console.error('[playRecording] Audio playback error:', e, audio.error);
      stopPlayback(); // Also stop on error
    });

    // Set source
    audio.src = audioUrl;

    // Load the audio
    audio.load();

    // Play immediately - removed setTimeout
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          console.log('[playRecording] Playback started successfully via promise.');
        })
        .catch(err => {
          console.error('[playRecording] Play promise rejected:', err);
          // Attempting to stop playback cleanly on error
          stopPlayback();
        });
    } else {
       console.warn('[playRecording] audio.play() did not return a promise.');
       // If no promise, playback might have started synchronously (older browsers)
       // or failed silently. State should reflect this eventually via events.
    }
  };

  const formatRecordingTime = (seconds: number) => {
    // During recording, just return the seconds
    return `${seconds}s`;
  };

  // Update the playRecentAudio function
  const playRecentAudio = (qaId: number, audioUrl: string, duration: number) => {
     // Initialize audio on first interaction or if context is suspended
    initializeAudio();
    if (audioContext?.state === 'suspended') {
      audioContext.resume().catch(e => console.error('[playRecentAudio] Error resuming suspended AudioContext:', e));
    }

    // Check if already playing this item - stop if that's the case
    if (playingQAId === qaId) {
      stopPlayback();
      return;
    }

    // Stop current audio if playing anything else
    stopPlayback(); // Use the centralized stop function

    const audio = new Audio();
    audioRef.current = audio; // Store ref immediately

    // Set states immediately
    setPlayingQAId(qaId);
    setPlaybackTime(duration); // Set initial playback time to total duration
    setPlaybackProgress(0);

    // Setup event listeners
    const updateHandler = () => {
      if (!audioRef.current) return;
      const currentTime = audioRef.current.currentTime;
      const remaining = Math.max(0, Math.ceil(duration - currentTime));

      // Update React state - which should trigger UI updates
      setPlaybackTime(remaining);
      setPlaybackProgress(Math.min(100, (currentTime / duration) * 100));
    };

    const endedHandler = () => {
      // Clean up event listeners to prevent memory leaks
      if (audioRef.current) {
        audioRef.current.removeEventListener('timeupdate', updateHandler);
        audioRef.current.removeEventListener('ended', endedHandler);
        audioRef.current.removeEventListener('error', errorHandler);
        // Make sure audio is fully stopped
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      // Force UI reset
      setPlayingQAId(null);
      setPlaybackProgress(0);
      setPlaybackTime(0);
    };

    const errorHandler = (e: Event) => {
      console.error('[playRecentAudio] Audio playback error:', e);
      // Clean up event listeners on error too
      if (audioRef.current) {
        audioRef.current.removeEventListener('timeupdate', updateHandler);
        audioRef.current.removeEventListener('ended', endedHandler);
        audioRef.current.removeEventListener('error', errorHandler);
      }
      stopPlayback();
    };

    // Add event listeners
    audio.addEventListener('timeupdate', updateHandler);
    audio.addEventListener('ended', endedHandler);
    audio.addEventListener('error', errorHandler);

    // Set source and load
    audio.src = audioUrl;
    audio.load();

    // Play the audio
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise.catch(err => {
        console.error('[playRecentAudio] Play promise error:', err);
        stopPlayback();
      });
    }
  };

  // --- Remove Log for Edit Modal Render Check ---
  // console.log('[Render] Checking editingItem for modal:', editingItem);
  // --- Remove Log for Save Button Render State ---
  // console.log('[Render] Save Button State:', { isSavingDocs, uploadedPdfsLength: uploadedPdfs.length, isDisabled: isSavingDocs || uploadedPdfs.length === 0 });

  // Remove function to handle doc save button click
  /*
  const handleDocSave = () => {
    if (uploadedPdfs.length === 0) {
      setDocUpdateMessage("No documents to upload. Add some documents first.")
      setDocUpdateStatus('error')
      setTimeout(() => setDocUpdateStatus('idle'), 3000)
      return
    }
    setShowDocConfirmation(true)
  }
  */

  // Remove save documents function
  /*
  const saveDocsToSupabase = async () => {
    setShowDocConfirmation(false);
    setIsDocUpdating(true);
    setDocUpdateStatus('loading');
    setDocUpdateProgress(0);
    setDocUpdateMessage('Processing uploads...');

    try {
      // --- Get User ID (auth.uid) from Session ---
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        console.error('Error fetching session:', sessionError);
        throw new Error('Could not verify user session. Please try again.');
      }
      if (!session?.user?.id) {
        throw new Error('User session not found or expired. Please log in again.');
      }
      const userId = session.user.id; // This is the auth UUID

      // --- Get Client ID (e.g., "CB0001") ---
      const clientId = getClientInfo()?.client_id;
      if (!clientId) {
        throw new Error('Client identifier not found. Please ensure you are properly logged in.');
      }

      // Store original uploaded PDFs to loop through
      const currentDocs = [...uploadedPdfs];
      const totalDocs = currentDocs.length;

      if (totalDocs === 0) {
        throw new Error('No documents found to upload. Please add some documents first.');
      }

      let uploadedCount = 0;

      // --- Stage 1: Upload PDF Files ---
      setDocUpdateMessage('Uploading documents...');

      for (let i = 0; i < currentDocs.length; i++) {
        const doc = currentDocs[i];
        setDocUpdateProgress(Math.round(((i + 1) / totalDocs) * 100));

        try {
          const file = doc.file;

          // Determine file extension (should be pdf, but checking to be safe)
          let fileExtension = 'pdf';
          if (file.name.includes('.')) {
            fileExtension = file.name.split('.').pop()?.toLowerCase() || 'pdf';
          }

          // Create unique file name with UUID
          const uniqueFileName = `${uuidv4()}.${fileExtension}`;

          // Use userId (auth UUID) for the storage path, exactly like audio upload
          const filePath = `${userId}/${uniqueFileName}`;

          // Upload to Supabase storage using same parameters as audio
          const { data: uploadData, error: uploadError } = await supabase
            .storage
            .from('docs')
            .upload(filePath, file, {
              cacheControl: '3600',
              upsert: false
            });

          if (uploadError) {
            // Parse error in the same format as audio upload
            const errorDetails = uploadError as any;
            const message = errorDetails.message || 'Upload failed.';
            const hint = errorDetails.error ? `(${errorDetails.error})` : '';
            throw new Error(`Failed to upload document "${doc.name.substring(0, 20)}...". ${message} ${hint}`);
          }

          // Get public URL - same method as audio
          const { data: urlData } = supabase
            .storage
            .from('docs')
            .getPublicUrl(filePath);

          if (!urlData?.publicUrl) {
            console.error(`Could not get public URL for uploaded document: ${filePath}`);
            throw new Error(`Failed to get URL for document "${doc.name.substring(0, 20)}...".`);
          }

          // Save reference to the docs table
          const { error: insertError } = await supabase
            .from('docs')
            .insert({
              client_id: clientId,
              doc_url: urlData.publicUrl,
              doc_file_path: filePath, // Add this field
              created_at: new Date()
            });

          if (insertError) {
            console.error('Error inserting document record:', insertError);
            throw new Error(`Failed to save document "${doc.name}". ${insertError.message}`);
          }

          uploadedCount++;
        } catch (error) {
          console.error(`Error processing document ${doc.name}:`, error);
          throw error;
        }
      }

      // Update document count after successful upload
      // await fetchDocCount(); // Removed call

      // Success handling
      setDocUpdateStatus('success');
      setDocUpdateMessage(`Successfully uploaded ${uploadedCount} document${uploadedCount > 1 ? 's' : ''}.`);

      // Clear the uploaded documents array and reset file input
      setUploadedPdfs([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      setTimeout(() => {
        setDocUpdateStatus('idle');
        setIsDocUpdating(false);
      }, 1500);

    } catch (error: any) {
      console.error('Error uploading documents:', error);
      setDocUpdateStatus('error');
      setDocUpdateMessage(error.message || 'Failed to upload documents. Please try again.');
      setIsDocUpdating(false);
    }
  }
  */

  // Add useEffect to close product results when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchResultsRef.current && !searchResultsRef.current.contains(event.target as Node)) {
        setShowPhotoResults(false)
      }
    }

    if (showPhotoResults) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPhotoResults])

  // Add state for all photos
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Add function to fetch all photos
  const fetchAllPhotos = async () => {
    try {
      // Wait for dashboard data to load before fetching photos
      if (!clientInfo?.client_id) {
        return
      }

      const clientId = clientInfo.client_id

      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .order('updated_at', { ascending: false })

      if (error) {
        console.error('Error fetching photos:', error)
        return
      }

      setAllPhotos(data || [])
    } catch (error) {
      console.error('Error in fetchAllPhotos:', error)
    }
  }

  // Add function to fetch photos from supabase
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([])
      setShowPhotoResults(false)
      return
    }

    setIsSearching(true)
    try {
      // First try to search locally
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5)

        if (filteredPhotos.length > 0) {
          setPhotoSearchResults(filteredPhotos)
          setShowPhotoResults(true)
          setIsSearching(false)
          return
        }
      }

      // If no local results or no local data, fetch from server
      if (!clientInfo?.client_id) {
        return
      }

      const clientId = clientInfo.client_id

      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .ilike('photo_id', `%${query}%`)
        .limit(5)

      if (error) {
        console.error('Error searching photos:', error)
        return
      }

      setPhotoSearchResults(data || [])
      setShowPhotoResults(data && data.length > 0)
    } catch (error) {
      console.error('Error in searchPhotos:', error)
    } finally {
      setIsSearching(false)
    }
  }

  // Add function to handle photo search input
  const handlePhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setPhotoSearchQuery(query)
    searchPhotos(query)
  }

  // Add function to select a photo
  const handleSelectPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null

    // Show loading animation
    setIsPhotoLoading(true)

    // Hide dropdown and clear search query immediately
    setShowPhotoResults(false)
    setPhotoSearchQuery('')

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedPhoto({
        id: photo.id,
        photo_id: photo.photo_id,
        photo_url: thumbnail,
        full_photo_urls: photo.photo_url // Store the complete array of photo URLs
      })

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false)
      }, 50)
    }, 100)
  }

  // Add function to clear selected photo
  const handleClearSelectedPhoto = () => {
    setSelectedPhoto(null)
  }

  const [showRecordingPopup, setShowRecordingPopup] = useState(false);

  // Handle viewing an image gallery
  const handleViewImage = (imageUrls: string[] | null | undefined) => {
    if (!imageUrls || imageUrls.length === 0) {
      // Show a notification that there are no images to display
      setUpdateStatus('error');
      setUpdateMessage('No images available for this item.');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      return;
    }

    setImageGallery({
      urls: imageUrls,
      currentIndex: 0
    });
  };

  // Navigate to previous image in gallery
  const showPreviousImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  };

  // Navigate to next image in gallery
  const showNextImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  };

  // Handle touch events for swipe in gallery
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      showNextImage();
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      showPreviousImage();
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  // Keyboard navigation for image gallery (arrow keys and escape)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'ArrowLeft') {
        showPreviousImage();
      } else if (event.key === 'ArrowRight') {
        showNextImage();
      } else if (event.key === 'Escape') {
        setImageGallery(null);
      }
    }

    if (imageGallery) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [imageGallery]);

  // Simple scroll disable when any popup is open
  useEffect(() => {
    const hasOpenModal = showConfirmation || editingItem || viewingItem || showRecordingPopup || imageGallery || showDeleteConfirmation || showAudioDeleteConfirmation || showAudioErrorPopup || (updateStatus !== 'idle')

    if (hasOpenModal) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [showConfirmation, editingItem, viewingItem, showRecordingPopup, imageGallery, showDeleteConfirmation, showAudioDeleteConfirmation, showAudioErrorPopup, updateStatus])



  // Add useEffect to fetch photos on page load - only after client info is available
  useEffect(() => {
    if (clientInfo?.client_id) {
      fetchAllPhotos()
    }
  }, [clientInfo?.client_id]) // Only run when client info is loaded

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
          >


            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>
              {/* <div className="flex items-center space-x-4">
                <span className="text-zinc-300 text-sm font-body">Knowledge Base</span>
              </div> */}
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <div>
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-zinc-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 group"
              >
                <svg
                  className="w-4 h-4 transform -translate-x-0.5 group-hover:-translate-x-1 transition-transform"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('ai_brain')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Statistics</h2> */}

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow1">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="rgba(0, 0, 0, 0.3)"
                        stroke="rgba(255, 255, 255, 0.1)"
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(255, 255, 255, 0.15)"
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray={`${Math.min((totalFaqs / (totalFaqsLimit || 1)) * 251.2, 251.2)} 251.2`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                        filter="url(#glow1)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center" style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-white/70 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('brain')}</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{totalFaqs} <span className="text-white/50">/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Photo Gallery Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow2">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="rgba(0, 0, 0, 0.3)"
                        stroke="rgba(255, 255, 255, 0.1)"
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(255, 255, 255, 0.15)"
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 - Math.min((photoCount / (photoLimit || 1)) * 251.2, 251.2)}
                        transform="rotate(-90 50 50)"
                        filter="url(#glow2)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center" style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-white/70 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('photo_gallery')}</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{photoCount} <span className="text-white/50">/ {photoLimit || 0}</span></>
                    }
                  </p>
                </div>
              </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Knowledge Management</h2> */}

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <Link
                  href="/dashboard/knowledge"
                  className="bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  style={{
                    boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                  }}
                >
                  {t('business_insight')}
                </Link>
                {/* <Link
                  href="/dashboard/knowledge/productInfo"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product Info
                </Link> */}

                <Link
                  href="/dashboard/knowledge/photo"
                  className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  // style={{
                  //   boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                  // }}
                >
                  {t('photo_gallery')}
                </Link>

                <Link
                  href="/dashboard/knowledge/introsOutros"
                  className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  // style={{
                  //   boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                  // }}
                >
                  {t('intro_outro')}
                </Link>

                {/* <Link
                  href="/dashboard/knowledge/productList"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product List
                </Link> */}
              </div>
              </div>
            </div>
          </div>

          {/* Business Insights Section */}
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >

            <div className="relative z-10">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-xl font-bold font-title">{t('business_insights')}</h2>
              <Link
                href="/dashboard/knowledge/knowledgeBase"
                className="bg-white/5 hover:bg-transparent text-white px-4 py-2 text-xs sm:text-base rounded-lg transition-colors font-body border border-jade-purple"
                // className="btn-glass-outline text-xs sm:text-base px-4 !py-2"
                style={{
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.1)',
                }}
              >
                {t('manage')}
              </Link>
            </div>
            <p className="text-zinc-300 text-sm mb-4 md:mb-6 font-body">
              {t('add_business_info')}
            </p>

            {/* Photo Search Bar */}
            <div className="mb-4 relative">
              <div className="relative">
                <input
                  type="text"
                  value={photoSearchQuery}
                  onChange={handlePhotoSearch}
                  onFocus={() => photoSearchResults.length > 0 && setShowPhotoResults(true)}
                  placeholder={t('search_photo_placeholder')}
                  className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-white/40"
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Search Results Dropdown */}
              {showPhotoResults && (
                <div
                  className="absolute z-50 w-full mt-2 bg-zinc-800/95 border border-white/10 rounded-xl shadow-2xl max-h-60 overflow-y-auto backdrop-blur-lg"
                  ref={searchResultsRef}
                  style={{
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 8px 10px -6px rgba(0, 0, 0, 0.3)'
                  }}
                >
                  {photoSearchResults.length > 0 ? (
                    photoSearchResults.map(photo => (
                      <div
                        key={photo.id}
                        className="flex items-center gap-3 p-3 hover:bg-white/5 cursor-pointer border-b border-white/5 last:border-0 transition-colors duration-200"
                        onClick={() => handleSelectPhoto(photo)}
                        style={{
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {/* Photo Thumbnail - Optimized */}
                        <PhotoThumbnail
                          photo={photo}
                          className="w-10 h-10"
                        />
                        {/* Photo ID */}
                        <div className="flex-1 truncate">
                          <p className="text-white truncate">{photo.photo_id}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-3 text-zinc-400 text-center">
                      {t('no_photos_found')}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Photo Display or Loading Animation */}
            {isPhotoLoading ? (
              <div className="mb-4 p-4 bg-white/10 border border-white/10 rounded-xl flex items-center justify-center h-16">
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-white/70 text-sm">{t('loading')}</span>
                </div>
              </div>
            ) : selectedPhoto && (
              <div className="mb-4 p-3 bg-white/10 border border-white/10 rounded-xl flex items-center justify-between animate-fadeIn">
                <div className="flex items-center gap-3">
                  {/* Photo Thumbnail */}
                  <div className="w-10 h-10 bg-black/50 rounded overflow-hidden flex-shrink-0 border border-white/20">
                    {selectedPhoto.photo_url ? (
                      <img
                        src={selectedPhoto.photo_url}
                        alt={selectedPhoto.photo_id}
                        className="w-full h-full object-cover cursor-pointer"
                        onClick={() => handleViewImage(selectedPhoto.full_photo_urls || (selectedPhoto.photo_url ? [selectedPhoto.photo_url] : null))}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-white/5 text-zinc-300">
                        <span>No Image</span>
                      </div>
                    )}
                  </div>
                  {/* Photo ID */}
                  <div>
                    <p className="text-white">{selectedPhoto.photo_id}</p>
                  </div>
                </div>
                {/* Remove Button */}
                <button
                  onClick={handleClearSelectedPhoto}
                  className="p-1 rounded-lg bg-white/5 hover:bg-white/10 text-zinc-300 hover:text-white border border-white/10 transition-colors duration-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 mb-6">
              {/* Question Trigger */}
              <div className="sm:col-span-5">
                <div
                  className="px-2 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-white/40 cursor-pointer hover:border-white/40 flex items-center min-h-[42px]"
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                  onClick={() => {
                    setEditingItem({
                      id: -1,
                      field: 'question',
                      value: question
                    });
                  }}
                >
                  {question ?
                    <span className="truncate">{question}</span> :
                    <span className="text-zinc-500 truncate">{t('enter_question')}</span>
                  }
                </div>
              </div>
              {/* Answer Trigger */}
              <div className="sm:col-span-5">
                <div
                  className={`px-2 pr-12 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-white/40 cursor-pointer hover:border-white/40 flex items-center min-h-[42px] relative`}
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                  onClick={() => {
                    if (isRecording || isPlaying === 'business' || businessAudioUrl) {
                      return;
                    }
                    setEditingItem({
                      id: -2,
                      field: 'answer',
                      value: answer
                    });
                  }}
                >
                  {/* Regular input display */}
                  {!isRecording && recordingFor !== 'business' && !businessAudioUrl && !isSaving && (
                    answer ?
                      <span className="truncate pr-10">{answer}</span> :
                      <span className="text-zinc-500 truncate pr-10">{t('enter_reply')}</span>
                  )}

                  {/* Recording in progress overlay - Commented out since we have recording animation popup window */}
                  {/* {isRecording && recordingFor === 'business' && (
                    <div className="absolute inset-0 bg-jade-purple/50 flex items-center justify-center z-10 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                        <span className="text-white font-mono">{formatRecordingTime(recordingTime)}</span>
                      </div>
                    </div>
                  )} */}

                  {/* Saving indicator overlay */}
                  {isSaving && !isRecording && (
                    <div className="absolute inset-0 border-white/20 flex items-center justify-center z-20 rounded-lg" style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}>
                      <div className="flex items-center space-x-3">
                        <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-white font-medium">{t('saving')}...</span>
                      </div>
                    </div>
                  )}

                  {/* Audio playback UI */}
                  {businessAudioUrl && !isRecording && !isSaving && (
                    <div className="absolute inset-0 flex items-center justify-between px-4 z-10 rounded-lg">
                      {/* Progress bar - Full width background */}
                      {isPlaying === 'business' && (
                        <div className="absolute inset-0 bg-jade-purple/90 z-0 rounded-lg" style={{
                          width: `${playbackProgress}%`,
                          transition: 'width 0.3s linear'
                        }}></div>
                      )}

                      <div className="flex items-center space-x-2 flex-grow z-10">
                        <button
                          type="button"
                          className="mr-2 p-1 rounded-full bg-jade-purple hover:bg-jade-purple-dark text-white touch-manipulation"
                          style={{ touchAction: "manipulation" }}
                          onClick={() => playRecording('business')}
                          onTouchEnd={(e) => {
                            e.preventDefault();
                            playRecording('business');
                          }}
                          aria-label={isPlaying === 'business' ? "Pause audio" : "Play audio"}
                        >
                          {isPlaying === 'business' ? (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <rect x="6" y="6" width="12" height="12" rx="2" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          )}
                        </button>
                        <span className="text-white truncate">
                          {t('audio')} ({isPlaying === 'business' ? playbackTime : audioDuration}s)
                        </span>
                      </div>

                      <div className="flex items-center space-x-2 z-10">
                        <button
                          type="button"
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (isPlaying === 'business') {
                              stopPlayback();
                            }
                            // Show confirmation modal instead of deleting immediately
                            setShowAudioDeleteConfirmation(true);
                          }}
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Microphone button */}
                  <button
                    type="button"
                    className="absolute right-2 p-1.5 rounded-full transition-colors bg-black/40 text-white/70 hover:bg-jade-purple hover:border-jade-purple-dark hover:text-white border border-white/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();

                      setShowRecordingPopup(true);
                      // Don't start recording here - wait for user to click Start Recording

                      return false;
                    }}
                    title="Record answer"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>

                  {/* Placeholder for Recording Popup Window - actual component moved to root level */}
                </div>
              </div>
              {/* Add Button */}
              <div className="sm:col-span-2 flex items-center justify-center">
                <button
                  onClick={handleAddQA}
                  className={`bg-jade-purple/90 text-white transition-all duration-200 px-6 py-2 text-xs sm:text-base rounded-lg font-medium w-full border border-white/20 ${
                    !question.trim() || (!answer.trim() && !businessAudioUrl) || isRecording || isSaving
                    ? 'opacity-70'
                    : 'hover:bg-jade-purple-dark hover:shadow-md'
                  }`}
                  style={{
                    boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                  }}
                  disabled={!question.trim() || (!answer.trim() && !businessAudioUrl) || isRecording || isSaving}
                >
                  {t('add')}
                </button>
              </div>
            </div>

            {/* Recently Added Section - now part of the same container */}
            <div className="flex justify-between items-center mb-3 border-t border-white/10 pt-6">
              <h3 className="text-lg font-semibold font-title">{t('recently_added')}</h3>
              <button
                onClick={handleUpdate}
                className={`bg-jade-purple/90 text-white transition-all duration-200 px-6 py-2 text-xs sm:text-base rounded-lg font-medium border border-white/20 ${
                  isUpdating || recentQA.length === 0
                  ? 'opacity-70'
                  : 'hover:bg-jade-purple-dark hover:shadow-md'
                }`}
                style={{
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                }}
                disabled={isUpdating || recentQA.length === 0}
              >
                {isUpdating ? t('updating') : t('update')}
              </button>
            </div>

            {/* Column Headers */}
            <div className="flex border-b border-white/10 py-3 font-semibold text-zinc-300 font-body">
              <div className="w-[5%] px-2 text-left"></div>
              <div className="w-[35%] px-2 text-left">{t('question')}</div>
              <div className="w-[35%] px-2 text-left">{t('reply')}</div>
              <div className="w-[15%] px-2 text-left"></div>
              <div className="w-[10%] px-2 text-center"></div>
            </div>

            {/* List Items */}
            <div className="w-full">
              {recentQA.length > 0 ? (
                recentQA.map((qa, index) => (
                  <div key={qa.id} className="flex border-b border-white/10 py-3 items-center">
                    <div className="w-[5%] px-2 text-left text-zinc-400 font-body">
                      {index + 1}
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      <div
                        className="h-full w-full truncate break-words px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg hover:bg-black/40 hover:border-white/40 flex items-center group cursor-pointer transition-all"
                        style={{
                          boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                        }}
                        title={qa.question}
                        onClick={() => handleStartEdit(qa.id, 'question', qa.question)}
                      >
                        <span className="flex-1">{qa.question}</span>
                        <svg className="w-4 h-4 ml-2 text-zinc-500 group-hover:text-jade-purple invisible group-hover:visible" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      {qa.audioUrl ? (
                        <div
                          className="h-full w-full px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg hover:bg-black/40 hover:border-white/40 flex items-center justify-between relative overflow-hidden cursor-pointer transition-all"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}
                        >
                          {/* Progress bar for playing audio */}
                          {playingQAId === qa.id && (
                            <div className="absolute inset-0 bg-jade-purple/30 z-0 rounded-lg" style={{
                              width: `${playbackProgress}%`,
                              transition: 'width 0.3s linear'
                            }}></div>
                          )}

                          <div className="flex items-center z-10 relative">
                            <button
                              className={`mr-2 p-1 rounded-full bg-jade-purple text-white z-10 relative touch-manipulation`}
                              style={{ touchAction: "manipulation" }}
                              onClick={(e) => {
                                e.stopPropagation(); // Keep stopping propagation
                                e.preventDefault();
                                playRecentAudio(qa.id, qa.audioUrl!, qa.audioDuration || 0);
                              }}
                              onTouchEnd={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                playRecentAudio(qa.id, qa.audioUrl!, qa.audioDuration || 0);
                              }}
                              aria-label={playingQAId === qa.id ? "Pause audio" : "Play audio"}
                            >
                              {playingQAId === qa.id ? (
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <rect x="6" y="6" width="12" height="12" rx="2" />
                                </svg>
                              ) : (
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              )}
                            </button>
                            <span className="flex-1">
                              {playingQAId === qa.id ?
                                `(${playbackTime}s)` : // This will now update when playbackTime changes
                                `(${qa.audioDuration || 0}s)`}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="h-full w-full truncate break-words px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg hover:bg-black/40 hover:border-white/40 flex items-center group cursor-pointer transition-all"
                          style={{
                            boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                          }}
                          title={qa.answer}
                          onClick={() => handleStartEdit(qa.id, 'answer', qa.answer)}
                        >
                          <span className="flex-1">{qa.answer}</span>
                          <svg className="w-4 h-4 ml-2 text-zinc-500 group-hover:text-jade-purple invisible group-hover:visible" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </div>
                      )}
                    </div>
                    {/* Photo Column */}
                    <div className="w-[15%] px-2 py-1">
                      {qa.photoInfo ? (
                        <div className="h-full w-full px-3 py-1.5 rounded-lg flex items-center justify-center" style={{
                          // boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                        }}>
                          {/* Photo Thumbnail - Optimized and clickable to view gallery */}
                          <PhotoThumbnail
                            photo={{
                              photo_url: qa.photoInfo.photo_url ? [qa.photoInfo.photo_url] : null,
                              photo_id: qa.photoInfo.photo_id
                            }}
                            className="w-10 h-10 border border-white/20"
                            onClick={() => handleViewImage(qa.photoInfo?.full_photo_urls || (qa.photoInfo?.photo_url ? [qa.photoInfo.photo_url] : null))}
                          />
                        </div>
                      ) : null}
                    </div>
                    <div className="w-[10%] px-2 flex justify-center">
                      <button
                        className="p-2 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-500 transition-colors"
                        onClick={() => handleDelete(qa.id)}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="py-8 text-center text-zinc-500">
                  {t('no_questions_update')}
                </div>
              )}
            </div>
            </div>
          </div>

          {/* <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
            <h2 className="text-xl font-bold mb-4 font-title">Performance Insights</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-zinc-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2 font-title">Response Accuracy</h3>
                <p className="text-3xl font-bold text-white">92%</p>
                <p className="text-zinc-400 text-sm">Based on customer satisfaction</p>
              </div>
              <div className="bg-zinc-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2 font-title">Knowledge Usage</h3>
                <p className="text-3xl font-bold text-white">748</p>
                <p className="text-zinc-400 text-sm">Times AI used your knowledge</p>
              </div>
              <div className="bg-zinc-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2 font-title">Coverage</h3>
                <p className="text-3xl font-bold text-white">86%</p>
                <p className="text-zinc-400 text-sm">Of questions can be answered</p>
              </div>
            </div>
          </div> */}
        </div>
      </div>

      {/* Keep FAQ Update Overlay */}
      {updateStatus !== 'idle' && (
        <div
          ref={faqStatusOverlayRef}
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
          >
          <div
              className={`relative p-6 rounded-2xl max-w-md w-full mx-4 backdrop-blur-xl overflow-hidden ${
              updateStatus === 'loading' ? 'bg-jade-purple/[0.1] border border-jade-purple/50 text-white' :
              updateStatus === 'success' ? 'bg-green-500/[0.1] border border-green-500/30 text-green-400' :
              'bg-red-500/[0.1] border border-red-500/30 text-red-400'
              }`}
              onClick={(e) => e.stopPropagation()}
          >

            <div className="relative z-10">
              {updateStatus === 'loading' ? (
                  <div className="flex flex-col items-center text-center">
                  <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-lg font-semibold mb-3">{t('updating')}...</p>
                  <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                    <div
                      className="bg-white h-3 rounded-full transition-all duration-300"
                      style={{ width: `${updateProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm">{updateProgress}% complete</p>
                  </div>
              ) : (
                  <div className="text-center">
                  <div className={updateStatus === 'success' ? 'text-green-400' : 'text-red-400'}>
                    {updateStatus === 'success' ? (
                      <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <p className="text-lg font-semibold mb-1">{updateStatus === 'success' ? t('success') : t('error')}</p>
                  <p>{updateMessage}</p>
                  </div>
              )}
              </div>
          </div>
          </div>
      )}

      {/* Remove Document Save Overlay */}
      {/* {docSaveStatus !== 'idle' && ( ... )} */}

      {/* Keep Confirmation Modals (only FAQ one matters now) */}
      {showConfirmation && (
         <div
           className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
         >
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
              onClick={(e: React.MouseEvent) => e.stopPropagation()}
            >

              <div className="relative z-10">
               <h3 className="text-xl font-bold mb-4 font-title text-center">
                 {t('update')} {t('knowledge')} {t('management')}
               </h3>
               <p className="text-white mb-6 text-center">
                 {t('confirm_update_questions').replace('{count}', recentQA.length.toString()).replace('{plural}', recentQA.length !== 1 ? 's' : '')}
               </p>
               <div className="flex space-x-3">
                 <button
                   onClick={() => setShowConfirmation(false)}
                   className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                   style={{
                     boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                   }}
                 >
                   {t('cancel')}
                 </button>
                 <button
                   onClick={saveQAToSupabase}
                   className="flex-1 py-2 bg-jade-purple text-white hover:bg-jade-purple-dark/75 hover:shadow-md hover:bg-jade-purple hover:border-jade-purple transition-all duration-200 rounded-lg font-medium border border-white/20"
                   style={{
                     boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                   }}
                 >
                   {t('confirm')}
                 </button>
               </div>
               </div>
            </div>
         </div>
      )}
      {/* Remove Doc Save Confirmation Modal */}
      {/* {showDocSaveConfirmation && ( ... )} */}

      {/* Keep Edit/View Modals */}
      {editingItem && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-[53]"
        >
          <div
            ref={modalRef}
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-lg mx-4 border border-white/20 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10" data-modal-content>
              {/* Close button (X) */}
              <button
                className="absolute top-0 right-0 p-1 rounded-full bg-black/40 hover:bg-jade-purple text-white/60 hover:text-white border border-white/20 transition-colors"
                onClick={() => setEditingItem(null)}
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <h3 className="text-xl text-white/80 font-bold mb-4 font-title text-center">
                {t('edit')} {editingItem.field === 'question' ? t('question') : t('reply')}
              </h3>
              <textarea
                ref={textareaRef}
                value={editingItem.value}
                onChange={(e) => {
                  // Only update if value actually changed
                  if (e.target.value !== editingItem.value) {
                    setEditingItem(prev => prev ? {...prev, value: e.target.value} : null);
                  }
                }}
                onClick={() => {
                  // On mobile: only position cursor at end on FIRST click (initial focus)
                  // After that, allow free cursor movement
                  if (!hasFocusedInput && textareaRef.current) {
                    const length = textareaRef.current.value.length;
                    textareaRef.current.setSelectionRange(length, length);
                    setHasFocusedInput(true);
                  }
                  // Subsequent clicks: let user position cursor freely (default browser behavior)
                }}
                placeholder={editingItem.field === 'question' ? t('enter_question') : t('enter_reply')}
                className="w-full px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-white/40 min-h-[100px] mb-4"
                style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
              />
              <button
                onClick={handleSaveEdit}
                className="bg-jade-purple text-white hover:bg-jade-purple-dark/75 hover:shadow-md hover:bg-jade-purple transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full border border-jade-purple/75"
                style={{
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                }}
              >
                {t('done')}
              </button>
            </div>
          </div>
        </div>
      )}
      {viewingItem && (
         <div
           className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
         >
           <div
             ref={viewModalRef}
             className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-lg mx-4 border border-white/20 overflow-hidden"
             onClick={(e: React.MouseEvent) => e.stopPropagation()}
           >

             <div className="relative z-10">
             <h3 className="text-xl text-white/80 font-bold mb-4 font-title text-center">
               {viewingItem.field === 'question' ? t('question') : t('reply')}
             </h3>
             <div className="bg-black/30 border border-white/20 rounded-lg p-4 text-white mb-4 max-h-[60vh] overflow-y-auto" style={{
               boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
             }}>
               {viewingItem.value}
             </div>
             <button
               onClick={() => setViewingItem(null)}
               className="bg-white/90 text-jade-purple hover:bg-white hover:shadow-md transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full border border-white/20"
               style={{
                 boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
               }}
             >
               {t('close')}
             </button>
             </div>
           </div>
         </div>
      )}

      {/* Remove Document Upload Confirmation Modal */}
      {/*
      {showDocConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="bg-zinc-900 border border-zinc-800 rounded-xl p-6 w-full max-w-md mx-4"
            ref={confirmModalRef} // Reuse ref, but be careful if both modals could be open
          >
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              Upload Documents
            </h3>
            <p className="text-zinc-300 mb-6 text-center">
              You are about to upload {uploadedPdfs.length} document{uploadedPdfs.length !== 1 ? 's' : ''} to your knowledge base?
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDocConfirmation(false)}
                className="flex-1 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={saveDocsToSupabase}
                className="flex-1 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
      */}

      {/* Remove Document Update Status Overlay */}
      {/*
      {docUpdateStatus !== 'idle' && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
          onClick={(e) => {
              if (docUpdateStatus === 'success' || docUpdateStatus === 'error') {
                setDocUpdateStatus('idle');
              }
          }}
        >
          <div
            className={`p-6 rounded-xl max-w-md w-full mx-4 ${
              docUpdateStatus === 'loading' ? 'bg-jade-purple/30 border border-jade-purple/50 text-white' :
              docUpdateStatus === 'success' ? 'bg-green-500/20 border border-green-500/30 text-green-400' :
              'bg-red-500/20 border border-red-500/30 text-red-400'
            } backdrop-blur-md`}
            onClick={(e) => e.stopPropagation()}
          >
            {docUpdateStatus === 'loading' ? (
              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                <p className="text-lg font-semibold mb-3">Uploading documents...</p>
                <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                  <div
                    className="bg-white h-3 rounded-full transition-all duration-300"
                    style={{ width: `${docUpdateProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm">{docUpdateProgress}% complete</p>
              </div>
            ) : (
              <div className="text-center">
                <div className={docUpdateStatus === 'success' ? 'text-green-400' : 'text-red-400'}>
                  {docUpdateStatus === 'success' ? (
                    <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )}
                </div>
                <p className="text-lg font-semibold mb-1">{docUpdateStatus === 'success' ? 'Success!' : 'Error'}</p>
                <p>{docUpdateMessage}</p>
              </div>
            )}
          </div>
        </div>
      )}
      */}

      <Footer />

      {/* Recording Popup Window */}
      {showRecordingPopup && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10">
            {/* Close button (X) */}
            <button
              className="absolute top-0 right-0 p-1 rounded-full bg-black/40 hover:bg-jade-purple text-white/60 hover:text-white border border-white/20 transition-colors"
              onClick={() => {
                if (isRecording) stopRecording();
                setShowRecordingPopup(false);
              }}
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {t('record_audio_answer')}
            </h3>

            <div className="flex justify-center items-center mb-6 h-32">
              <div
                className={`relative w-32 h-32 rounded-full flex items-center justify-center ${isRecording ? 'bg-red-500/20' : 'bg-black/30'} border-2 ${isRecording ? 'border-red-500' : 'border-white/20'}`}
              >
                <div className="text-center">
                  <div className="text-2xl font-mono">
                    {isRecording ? formatRecordingTime(recordingTime) : "0s"}
                  </div>
                  <div className="text-sm text-white/60 mt-1">
                    {isRecording ? (
                      <span className="inline-flex items-center">
                        <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-1.5"></span>
                        {t('recording')}
                      </span>
                    ) : (
                      t('ready')
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-center gap-4">
              {!isRecording ? (
                <button
                  onClick={() => startRecording('business')}
                  className="py-2.5 px-6 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg flex items-center justify-center border-2 border-jade-purple/80 transition-all duration-200 hover:shadow-lg hover:shadow-jade-purple/30"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  {t('start_recording')}
                </button>
              ) : (
                <button
                  onClick={() => {
                    stopRecording();
                    setShowRecordingPopup(false);
                  }}
                  className="py-2.5 px-6 bg-red-500 hover:bg-red-600 text-white rounded-lg flex items-center justify-center border-2 border-red-500/80 transition-all duration-200 hover:shadow-lg hover:shadow-red-500/30"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                  </svg>
                  {t('stop_recording')}
                </button>
              )}
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Gallery Modal */}
      {imageGallery && (
        <div
          className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            ref={imageGalleryRef}
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-3xl mx-4 border border-white/20 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10">
            {/* Close button in the top-right corner */}
            <button
              onClick={() => setImageGallery(null)}
              className="absolute top-0 right-0 p-1.5 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex flex-col">
              {/* Image counter */}
              <div className="text-center mb-2 text-sm text-zinc-400">
                {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
              </div>

              {/* Main image container with touch events */}
              <div
                className="relative h-[60vh] flex items-center justify-center"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                aria-live="polite"
                role="region"
                aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
              >
                {imageGallery.urls.length === 0 ? (
                  <div className="text-center text-zinc-400 p-8 bg-zinc-800/50 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p>{t('no_images_available')}</p>
                  </div>
                ) : (
                  <div className="relative">
                    <img
                      src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                      alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                      className="rounded-lg max-w-full max-h-full object-contain"
                      loading="eager"
                      decoding="async"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                      }}
                    />
                  </div>
                )}

                {/* Navigation buttons - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={showPreviousImage}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Previous image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={showNextImage}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Next image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
              </div>

              {/* Thumbnail strip - only show if more than one image */}
              {imageGallery.urls.length > 1 && (
                <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                  {imageGallery.urls.map((url, index) => (
                    <PhotoThumbnail
                      key={index}
                      photo={{
                        photo_url: [url],
                        photo_id: `thumbnail-${index + 1}`
                      }}
                      className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                        index === imageGallery.currentIndex ? 'border-white/80 scale-105 opacity-100' : 'border-zinc-700 opacity-70 hover:opacity-90 hover:border-zinc-600'
                      }`}
                      onClick={() => {
                        setImageGallery({ ...imageGallery, currentIndex: index });
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Editing Item Modal */}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            <div className="relative z-10">
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {t('delete_item')}
            </h3>

            <p className="text-white/80 mb-6 text-center">
              {t('delete_confirmation')}
            </p>

            <div className="flex justify-between w-full space-x-4">
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"
                style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
              >
                {t('cancel')}
              </button>
              <button
                onClick={confirmDelete}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"
                style={{
                  boxShadow: '0 0 15px rgba(239, 68, 68, 0.3)'
                }}
              >
                {t('delete')}
              </button>
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Audio Delete Confirmation Modal */}
      {showAudioDeleteConfirmation && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            <div className="relative z-10">
            <h3 className="text-xl font-bold mb-4 font-title text-center">
              {t('delete_audio')}
            </h3>

            <p className="text-white/80 mb-6 text-center">
              {t('delete_audio_confirmation')}
            </p>

            <div className="flex justify-between w-full space-x-4">
              <button
                onClick={() => setShowAudioDeleteConfirmation(false)}
                className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"
                style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
              >
                {t('cancel')}
              </button>
              <button
                onClick={confirmAudioDelete}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"
                style={{
                  boxShadow: '0 0 15px rgba(239, 68, 68, 0.3)'
                }}
              >
                {t('delete')}
              </button>
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Audio Error Popup */}
      {showAudioErrorPopup && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <div
            className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
            onClick={(e: React.MouseEvent) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
          >
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            <div className="relative z-10">
            {/* Close button (X) in the top-right corner */}
            <button
              onClick={handleCloseAudioErrorPopup}
              className="absolute top-0 right-0 p-1.5 rounded-full bg-black/40 hover:bg-black/60 text-red-400 hover:text-white border border-red-400/20 transition-colors"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="text-center">
              <div className="text-red-400">
                <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-lg font-semibold mb-1 text-red-400">{t('audio_recording_error')}</p>
              <p className="text-white">{t('audio_length_error')}</p>
            </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
